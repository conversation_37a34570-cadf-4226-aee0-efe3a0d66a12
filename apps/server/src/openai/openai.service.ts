import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenA<PERSON> from 'openai';
import { JobData } from '@rapidresume/job-parser';
import { ResumeData } from '@reactive-resume/schema';
import { Config } from '../config/schema';

export interface RewriteResumeRequest {
  baselineResume: ResumeData;
  jobData: JobData;
}

export interface RewriteResumeResponse {
  success: boolean;
  data?: ResumeData;
  error?: string;
}

@Injectable()
export class OpenAIService {
  private readonly logger = new Logger(OpenAIService.name);
  private readonly openai: OpenAI;

  constructor(private readonly configService: ConfigService<Config>) {
    const apiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!apiKey) {
      this.logger.warn('OPENAI_API_KEY not configured. OpenAI features will be disabled.');
    }
    
    this.openai = new OpenAI({
      apiKey: apiKey || 'dummy-key',
    });
  }

  async rewriteResumeForJob(request: RewriteResumeRequest): Promise<RewriteResumeResponse> {
    try {
      const apiKey = this.configService.get<string>('OPENAI_API_KEY');
      if (!apiKey) {
        return {
          success: false,
          error: 'OpenAI API key not configured',
        };
      }

      this.logger.log(`Rewriting resume for job: ${request.jobData.title} at ${request.jobData.company}`);

      const prompt = this.buildRewritePrompt(request.baselineResume, request.jobData);
      
      const completion = await this.openai.chat.completions.create({
        model: 'gpt-4o',
        messages: [
          {
            role: 'system',
            content: 'You are an expert resume writer. Your task is to rewrite resume bullet points to maximize keyword overlap with a job posting while keeping all numbers and achievements intact. Return only valid JSON matching the input resume structure.',
          },
          {
            role: 'user',
            content: prompt,
          },
        ],
        temperature: 0.3,
        max_tokens: 4000,
      });

      const responseContent = completion.choices[0]?.message?.content;
      if (!responseContent) {
        return {
          success: false,
          error: 'No response from OpenAI',
        };
      }

      // Parse the JSON response
      let rewrittenResume: ResumeData;
      try {
        rewrittenResume = JSON.parse(responseContent);
      } catch (parseError) {
        this.logger.error('Failed to parse OpenAI response as JSON:', parseError);
        return {
          success: false,
          error: 'Invalid JSON response from OpenAI',
        };
      }

      // Validate that the response has the expected structure
      if (!this.validateResumeStructure(rewrittenResume)) {
        return {
          success: false,
          error: 'Invalid resume structure in OpenAI response',
        };
      }

      this.logger.log('Successfully rewrote resume using OpenAI');
      return {
        success: true,
        data: rewrittenResume,
      };
    } catch (error) {
      this.logger.error('Error rewriting resume with OpenAI:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  private buildRewritePrompt(baselineResume: ResumeData, jobData: JobData): string {
    const experienceItems = baselineResume.sections.experience?.items || [];
    const projectItems = baselineResume.sections.projects?.items || [];
    
    return `
Job Posting Information:
Title: ${jobData.title}
Company: ${jobData.company}
Requirements: ${jobData.requirements.join(', ')}
Responsibilities: ${jobData.responsibilities.join(', ')}

Current Resume Data (JSON):
${JSON.stringify(baselineResume, null, 2)}

Instructions:
1. Rewrite or reorder bullet points from the experience and projects sections to maximize keyword overlap with the job posting
2. Keep all numbers, percentages, and quantifiable achievements intact
3. Remove fluff words and focus on impact and relevant skills
4. Ensure bullet points align with the job requirements and responsibilities
5. Maintain the exact JSON structure of the input resume
6. Only modify the 'summary' field in bullet points within experience and projects items
7. Do not change any other fields like dates, titles, company names, etc.
8. Return the complete resume JSON with your modifications

Return only the modified resume JSON, no additional text or explanation.
    `.trim();
  }

  private validateResumeStructure(resume: any): resume is ResumeData {
    // Basic validation to ensure the resume has the expected structure
    return (
      resume &&
      typeof resume === 'object' &&
      resume.sections &&
      typeof resume.sections === 'object' &&
      resume.basics &&
      typeof resume.basics === 'object'
    );
  }

  async testConnection(): Promise<boolean> {
    try {
      const apiKey = this.configService.get<string>('OPENAI_API_KEY');
      if (!apiKey) {
        return false;
      }

      const completion = await this.openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: 'Hello' }],
        max_tokens: 5,
      });

      return !!completion.choices[0]?.message?.content;
    } catch (error) {
      this.logger.error('OpenAI connection test failed:', error);
      return false;
    }
  }
}
