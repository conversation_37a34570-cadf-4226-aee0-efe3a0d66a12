import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  Logger,
} from "@nestjs/common";
import { Prisma } from "@prisma/client";
import { CreateResumeDto, GenerateResumeDto, GenerateResumeResponseDto, ImportResumeDto, ResumeDto, UpdateResumeDto } from "@reactive-resume/dto";
import { defaultResumeData, ResumeData } from "@reactive-resume/schema";
import type { DeepPartial } from "@reactive-resume/utils";
import { ErrorMessage, generateRandomName } from "@reactive-resume/utils";
import slugify from "@sindresorhus/slugify";
import deepmerge from "deepmerge";
import { PrismaService } from "nestjs-prisma";

import { PrinterService } from "@/server/printer/printer.service";
import { OpenAIService } from "../openai/openai.service";
import { JobParserService } from "@rapidresume/job-parser";

import { StorageService } from "../storage/storage.service";

@Injectable()
export class ResumeService {
  private readonly logger = new Logger(ResumeService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly printerService: PrinterService,
    private readonly storageService: StorageService,
    private readonly openaiService: OpenAIService,
    private readonly jobParserService: JobParserService,
  ) {}

  async create(userId: string, createResumeDto: CreateResumeDto) {
    const { name, email, picture } = await this.prisma.user.findUniqueOrThrow({
      where: { id: userId },
      select: { name: true, email: true, picture: true },
    });

    const data = deepmerge(defaultResumeData, {
      basics: { name, email, picture: { url: picture ?? "" } },
    } satisfies DeepPartial<ResumeData>);

    return this.prisma.resume.create({
      data: {
        data,
        userId,
        title: createResumeDto.title,
        visibility: createResumeDto.visibility,
        slug: createResumeDto.slug ?? slugify(createResumeDto.title),
      },
    });
  }

  import(userId: string, importResumeDto: ImportResumeDto) {
    const randomTitle = generateRandomName();

    return this.prisma.resume.create({
      data: {
        userId,
        visibility: "private",
        data: importResumeDto.data,
        title: importResumeDto.title ?? randomTitle,
        slug: importResumeDto.slug ?? slugify(randomTitle),
      },
    });
  }

  findAll(userId: string) {
    return this.prisma.resume.findMany({ where: { userId }, orderBy: { updatedAt: "desc" } });
  }

  findOne(id: string, userId?: string) {
    if (userId) {
      return this.prisma.resume.findUniqueOrThrow({ where: { userId_id: { userId, id } } });
    }

    return this.prisma.resume.findUniqueOrThrow({ where: { id } });
  }

  async findOneStatistics(id: string) {
    const result = await this.prisma.statistics.findFirst({
      select: { views: true, downloads: true },
      where: { resumeId: id },
    });

    return {
      views: result?.views ?? 0,
      downloads: result?.downloads ?? 0,
    };
  }

  async findOneByUsernameSlug(username: string, slug: string, userId?: string) {
    const resume = await this.prisma.resume.findFirstOrThrow({
      where: { user: { username }, slug, visibility: "public" },
    });

    // Update statistics: increment the number of views by 1
    if (!userId) {
      await this.prisma.statistics.upsert({
        where: { resumeId: resume.id },
        create: { views: 1, downloads: 0, resumeId: resume.id },
        update: { views: { increment: 1 } },
      });
    }

    return resume;
  }

  async update(userId: string, id: string, updateResumeDto: UpdateResumeDto) {
    try {
      const { locked } = await this.prisma.resume.findUniqueOrThrow({
        where: { id },
        select: { locked: true },
      });

      if (locked) throw new BadRequestException(ErrorMessage.ResumeLocked);

      return await this.prisma.resume.update({
        data: {
          title: updateResumeDto.title,
          slug: updateResumeDto.slug,
          visibility: updateResumeDto.visibility,
          data: updateResumeDto.data as Prisma.JsonObject,
        },
        where: { userId_id: { userId, id } },
      });
    } catch (error) {
      if (error.code === "P2025") {
        Logger.error(error);
        throw new InternalServerErrorException(error);
      }
    }
  }

  lock(userId: string, id: string, set: boolean) {
    return this.prisma.resume.update({
      data: { locked: set },
      where: { userId_id: { userId, id } },
    });
  }

  async remove(userId: string, id: string) {
    await Promise.all([
      // Remove files in storage, and their cached keys
      this.storageService.deleteObject(userId, "resumes", id),
      this.storageService.deleteObject(userId, "previews", id),
    ]);

    return this.prisma.resume.delete({ where: { userId_id: { userId, id } } });
  }

  async printResume(resume: ResumeDto, userId?: string) {
    const url = await this.printerService.printResume(resume);

    // Update statistics: increment the number of downloads by 1
    if (!userId) {
      await this.prisma.statistics.upsert({
        where: { resumeId: resume.id },
        create: { views: 0, downloads: 1, resumeId: resume.id },
        update: { downloads: { increment: 1 } },
      });
    }

    return url;
  }

  printPreview(resume: ResumeDto) {
    return this.printerService.printPreview(resume);
  }

  async exportDocx(resume: ResumeDto, userId?: string) {
    const url = await this.printerService.exportDocx(resume);

    // Update statistics: increment the number of downloads by 1
    if (!userId) {
      await this.prisma.statistics.upsert({
        where: { resumeId: resume.id },
        create: { views: 0, downloads: 1, resumeId: resume.id },
        update: { downloads: { increment: 1 } },
      });
    }

    return url;
  }

  async generateResume(userId: string, generateResumeDto: GenerateResumeDto, file?: Express.Multer.File): Promise<GenerateResumeResponseDto> {
    try {
      this.logger.log(`Generating resume for user ${userId}`);

      // Step 1: Validate input - either jobUrl or file must be provided
      if (!generateResumeDto.jobUrl && !file) {
        return {
          success: false,
          error: 'Either job URL or file must be provided',
        };
      }

      // Step 2: Parse the job posting
      let fileBuffer: Buffer | undefined;
      if (file) {
        // Validate file type
        if (file.mimetype !== 'application/pdf') {
          return {
            success: false,
            error: 'Only PDF files are supported',
          };
        }

        // Validate file size (10MB limit)
        if (file.size > 10 * 1024 * 1024) {
          return {
            success: false,
            error: 'File size must be less than 10MB',
          };
        }

        fileBuffer = file.buffer;
        this.logger.log(`Processing uploaded PDF file: ${file.originalname}`);
      }

      const jobParseResult = await this.jobParserService.parseJob({
        jobUrl: generateResumeDto.jobUrl,
        fileBuffer,
      });

      if (!jobParseResult.success || !jobParseResult.data) {
        return {
          success: false,
          error: jobParseResult.error || 'Failed to parse job posting',
        };
      }

      const jobData = jobParseResult.data;
      this.logger.log(`Parsed job: ${jobData.title} at ${jobData.company}`);

      // Step 3: Get baseline resume
      let baselineResume: any;
      if (generateResumeDto.baselineResumeId) {
        baselineResume = await this.findOne(generateResumeDto.baselineResumeId, userId);
      } else {
        // Get user's most recent resume
        const resumes = await this.findAll(userId);
        if (resumes.length === 0) {
          return {
            success: false,
            error: 'No baseline resume found. Please create a resume first.',
          };
        }
        baselineResume = resumes[0]; // Most recent resume
      }

      // Step 4: Rewrite resume using OpenAI
      const rewriteResult = await this.openaiService.rewriteResumeForJob({
        baselineResume: baselineResume.data,
        jobData,
      });

      if (!rewriteResult.success || !rewriteResult.data) {
        return {
          success: false,
          error: rewriteResult.error || 'Failed to rewrite resume',
        };
      }

      // Step 5: Create new resume with generated content
      const generatedTitle = `${jobData.title} - ${jobData.company}`;
      const generatedSlug = slugify(generatedTitle);

      const newResume = await this.prisma.resume.create({
        data: {
          userId,
          title: generatedTitle,
          slug: generatedSlug,
          data: rewriteResult.data as Prisma.JsonObject,
          visibility: 'private',
          generatedResume: {
            create: {
              jobTitle: jobData.title,
              company: jobData.company,
              jobUrl: generateResumeDto.jobUrl,
              baselineResumeId: generateResumeDto.baselineResumeId || baselineResume.id,
            },
          },
        },
      });

      this.logger.log(`Generated resume ${newResume.id} for job: ${jobData.title}`);

      return {
        success: true,
        resumeId: newResume.id,
        jobTitle: jobData.title,
        company: jobData.company,
      };
    } catch (error) {
      this.logger.error('Error generating resume:', error);

      if (error instanceof BadRequestException) {
        throw error;
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  async getGeneratedResumeHistory(userId: string) {
    try {
      const generatedResumes = await this.prisma.resume.findMany({
        where: {
          userId,
          generatedResume: {
            isNot: null,
          },
        },
        include: {
          generatedResume: true,
          statistics: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return generatedResumes.map((resume) => ({
        id: resume.id,
        title: resume.title,
        slug: resume.slug,
        jobTitle: resume.generatedResume?.jobTitle,
        company: resume.generatedResume?.company,
        jobUrl: resume.generatedResume?.jobUrl,
        createdAt: resume.createdAt,
        views: resume.statistics?.views || 0,
        downloads: resume.statistics?.downloads || 0,
      }));
    } catch (error) {
      this.logger.error('Error fetching generated resume history:', error);
      throw new BadRequestException('Failed to fetch generated resume history');
    }
  }
}
