import { t } from "@lingui/macro";
import { 
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Badge,
} from "@reactive-resume/ui";
import { Download, ArrowSquareOut, Eye } from "@phosphor-icons/react";
import { useQuery } from "@tanstack/react-query";
import { Link } from "react-router";

import { getGeneratedResumeHistory, GeneratedResumeHistoryItem } from "../../../services/resume/history";

export const HistoryTab = () => {
  const { data: history, isLoading, error } = useQuery({
    queryKey: ['generated-resume-history'],
    queryFn: getGeneratedResumeHistory,
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">{t`Loading history...`}</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-destructive mb-4">{t`Failed to load history`}</p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            {t`Try Again`}
          </Button>
        </div>
      </div>
    );
  }

  if (!history || history.length === 0) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">{t`No generated resumes yet`}</p>
          <p className="text-sm text-muted-foreground">
            {t`Generate your first resume from a job posting to see it here.`}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">{t`Generated Resume History`}</h3>
        <Badge variant="secondary">{history.length} {t`resumes`}</Badge>
      </div>

      <div className="grid gap-4">
        {history.map((item) => (
          <HistoryItem key={item.id} item={item} />
        ))}
      </div>
    </div>
  );
};

interface HistoryItemProps {
  item: GeneratedResumeHistoryItem;
}

const HistoryItem = ({ item }: HistoryItemProps) => {
  const handleDownloadPDF = () => {
    window.open(`/api/resume/print/${item.id}`, '_blank');
  };

  const handleDownloadDocx = () => {
    window.open(`/api/resume/export/${item.id}/docx`, '_blank');
  };

  const handleViewResume = () => {
    window.open(`/builder/${item.id}`, '_blank');
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <CardTitle className="text-base">{item.title}</CardTitle>
            <CardDescription>
              {item.jobTitle && item.company && (
                <span className="font-medium">
                  {item.jobTitle} at {item.company}
                </span>
              )}
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              size="sm"
              variant="outline"
              onClick={handleViewResume}
              className="h-8"
            >
              <ArrowSquareOut className="h-3 w-3 mr-1" />
              {t`View`}
            </Button>
            <Button
              size="sm"
              onClick={handleDownloadPDF}
              className="h-8"
            >
              <Download className="h-3 w-3 mr-1" />
              {t`PDF`}
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={handleDownloadDocx}
              className="h-8"
            >
              <Download className="h-3 w-3 mr-1" />
              {t`DOCX`}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="flex items-center justify-between text-sm text-muted-foreground">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <Eye className="h-3 w-3 mr-1" />
              {item.views} {t`views`}
            </span>
            <span className="flex items-center">
              <Download className="h-3 w-3 mr-1" />
              {item.downloads} {t`downloads`}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            {item.jobUrl && (
              <a
                href={item.jobUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:underline text-xs"
              >
                {t`View Job Posting`}
              </a>
            )}
            <span className="text-xs">
              {new Date(item.createdAt).toLocaleDateString()}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
