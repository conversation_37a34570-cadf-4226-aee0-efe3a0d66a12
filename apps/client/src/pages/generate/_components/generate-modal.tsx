import { t } from "@lingui/macro";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  Input,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@reactive-resume/ui";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { toast } from "@/client/hooks/use-toast";
import { generateResume } from "../../../services/resume/generate";

const generateFormSchema = z.object({
  jobUrl: z.string().url().optional(),
  file: z.instanceof(File).optional(),
}).refine(
  (data) => data.jobUrl || data.file,
  {
    message: "Either job URL or file must be provided",
    path: ["jobUrl"],
  }
);

type GenerateFormData = z.infer<typeof generateFormSchema>;

interface GenerateModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const GenerateModal = ({ isOpen, onClose }: GenerateModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("url");

  const form = useForm<GenerateFormData>({
    resolver: zodResolver(generateFormSchema),
    defaultValues: {
      jobUrl: "",
      file: undefined,
    },
  });

  const onSubmit = async (data: GenerateFormData) => {
    setIsLoading(true);
    
    try {
      let result;
      
      if (activeTab === "url" && data.jobUrl) {
        result = await generateResume({ jobUrl: data.jobUrl });
      } else if (activeTab === "file" && data.file) {
        // For now, we'll show an error as file upload isn't fully implemented
        toast({
          variant: "error",
          title: t`File Upload Not Available`,
          description: t`File upload is not yet implemented. Please use URL input.`,
        });
        return;
      } else {
        toast({
          variant: "error",
          title: t`Missing Input`,
          description: t`Please provide either a job URL or upload a file.`,
        });
        return;
      }

      if (result.success) {
        toast({
          variant: "success",
          title: t`Resume Generated Successfully!`,
          description: t`Resume generated successfully for ${result.jobTitle} at ${result.company}!`,
        });
        onClose();
        form.reset();

        // Redirect to the generated resume
        if (result.resumeId) {
          window.location.href = `/builder/${result.resumeId}`;
        }
      } else {
        toast({
          variant: "error",
          title: t`Generation Failed`,
          description: result.error || t`Failed to generate resume`,
        });
      }
    } catch (error) {
      console.error("Error generating resume:", error);
      toast({
        variant: "error",
        title: t`Unexpected Error`,
        description: t`An unexpected error occurred. Please try again.`,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      if (file.type !== "application/pdf") {
        toast({
          variant: "error",
          title: t`Invalid File Type`,
          description: t`Please upload a PDF file.`,
        });
        return;
      }
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast({
          variant: "error",
          title: t`File Too Large`,
          description: t`File size must be less than 10MB.`,
        });
        return;
      }
      form.setValue("file", file);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{t`Generate Resume from Job Posting`}</DialogTitle>
          <DialogDescription>
            {t`Provide a job posting URL or upload a PDF to generate a tailored resume.`}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="url">{t`Job URL`}</TabsTrigger>
                <TabsTrigger value="file">{t`Upload PDF`}</TabsTrigger>
              </TabsList>

              <TabsContent value="url" className="space-y-4">
                <FormField
                  control={form.control}
                  name="jobUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t`Job Posting URL`}</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="https://example.com/job-posting"
                          {...field}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <p className="text-sm text-muted-foreground">
                  {t`Paste the URL of the job posting you want to apply for. We'll extract the job requirements and tailor your resume accordingly.`}
                </p>
              </TabsContent>

              <TabsContent value="file" className="space-y-4">
                <FormField
                  control={form.control}
                  name="file"
                  render={() => (
                    <FormItem>
                      <FormLabel>{t`Job Posting PDF`}</FormLabel>
                      <FormControl>
                        <Input
                          type="file"
                          accept=".pdf"
                          onChange={handleFileChange}
                          disabled={isLoading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <p className="text-sm text-muted-foreground">
                  {t`Upload a PDF file of the job posting. Maximum file size is 10MB.`}
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                  <p className="text-sm text-yellow-800">
                    {t`Note: File upload is currently under development. Please use the URL option for now.`}
                  </p>
                </div>
              </TabsContent>
            </Tabs>

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isLoading}
              >
                {t`Cancel`}
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? t`Generating...` : t`Generate Resume`}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
