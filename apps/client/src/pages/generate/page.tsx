import { t } from "@lingui/macro";
import { useLingui } from "@lingui/react";
import { Button } from "@reactive-resume/ui";
import { useState } from "react";
import { Helmet } from "react-helmet-async";
import { Link } from "react-router";

import { GenerateModal } from "./_components/generate-modal";

export const GeneratePage = () => {
  const { i18n } = useLingui();
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <div className="container mx-auto px-4 py-8">
      <Helmet prioritizeSeoTags>
        <html lang={i18n.locale} />
        <title>
          {t`Generate Resume`} - {t`RapidResume`}
        </title>
        <meta
          name="description"
          content="Generate a tailored resume from job postings using AI"
        />
      </Helmet>

      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">
            {t`Generate Your Perfect Resume`}
          </h1>
          <p className="text-lg text-muted-foreground mb-8">
            {t`Upload a job posting or paste a URL, and we'll create a tailored resume that maximizes your chances of getting hired.`}
          </p>
          
          <Button 
            size="lg" 
            onClick={() => setIsModalOpen(true)}
            className="mb-8"
          >
            {t`Start Generating`}
          </Button>
        </div>

        <div className="grid md:grid-cols-2 gap-8 mb-8">
          <div className="bg-card p-6 rounded-lg border">
            <h3 className="text-xl font-semibold mb-4">{t`How it works`}</h3>
            <ol className="space-y-3 text-sm">
              <li className="flex items-start">
                <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">1</span>
                {t`Provide a job posting URL or upload a PDF`}
              </li>
              <li className="flex items-start">
                <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">2</span>
                {t`Our AI analyzes the job requirements`}
              </li>
              <li className="flex items-start">
                <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">3</span>
                {t`We rewrite your resume to match the job`}
              </li>
              <li className="flex items-start">
                <span className="bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center text-xs mr-3 mt-0.5">4</span>
                {t`Download your tailored resume as PDF or DOCX`}
              </li>
            </ol>
          </div>

          <div className="bg-card p-6 rounded-lg border">
            <h3 className="text-xl font-semibold mb-4">{t`Features`}</h3>
            <ul className="space-y-3 text-sm">
              <li className="flex items-center">
                <span className="text-green-500 mr-3">✓</span>
                {t`AI-powered keyword optimization`}
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-3">✓</span>
                {t`Preserves your achievements and numbers`}
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-3">✓</span>
                {t`Multiple export formats (PDF, DOCX)`}
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-3">✓</span>
                {t`Resume history and quick downloads`}
              </li>
              <li className="flex items-center">
                <span className="text-green-500 mr-3">✓</span>
                {t`Works with any job posting`}
              </li>
            </ul>
          </div>
        </div>

        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-4">
            {t`Need to create a baseline resume first?`}
          </p>
          <Link to="/dashboard/resumes">
            <Button variant="outline">
              {t`Go to Resume Builder`}
            </Button>
          </Link>
        </div>
      </div>

      <GenerateModal 
        isOpen={isModalOpen} 
        onClose={() => setIsModalOpen(false)} 
      />
    </div>
  );
};
