import { axios } from "@/client/libs/axios";

export interface GeneratedResumeHistoryItem {
  id: string;
  title: string;
  slug: string;
  jobTitle?: string;
  company?: string;
  jobUrl?: string;
  createdAt: string;
  views: number;
  downloads: number;
}

export const getGeneratedResumeHistory = async (): Promise<GeneratedResumeHistoryItem[]> => {
  const response = await axios.get<GeneratedResumeHistoryItem[]>('/resume/generated/history');
  return response.data;
};
