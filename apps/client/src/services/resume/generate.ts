import { axios } from "@/client/libs/axios";

export interface GenerateResumeRequest {
  jobUrl?: string;
  file?: File;
  baselineResumeId?: string;
}

export interface GenerateResumeResponse {
  success: boolean;
  resumeId?: string;
  jobTitle?: string;
  company?: string;
  error?: string;
}

export const generateResume = async (request: GenerateResumeRequest): Promise<GenerateResumeResponse> => {
  // If we have a file, use FormData to upload it
  if (request.file) {
    const formData = new FormData();
    formData.append('file', request.file);
    if (request.baselineResumeId) {
      formData.append('baselineResumeId', request.baselineResumeId);
    }

    const response = await axios.post<GenerateResumeResponse>('/resume/generate', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  // Otherwise, send JSON data for URL-based generation
  const response = await axios.post<GenerateResumeResponse>('/resume/generate', {
    jobUrl: request.jobUrl,
    baselineResumeId: request.baselineResumeId,
  });
  return response.data;
};
