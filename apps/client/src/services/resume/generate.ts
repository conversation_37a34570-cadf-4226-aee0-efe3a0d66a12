import { axios } from "@/client/libs/axios";

export interface GenerateResumeRequest {
  jobUrl?: string;
  fileId?: string;
  baselineResumeId?: string;
}

export interface GenerateResumeResponse {
  success: boolean;
  resumeId?: string;
  jobTitle?: string;
  company?: string;
  error?: string;
}

export const generateResume = async (request: GenerateResumeRequest): Promise<GenerateResumeResponse> => {
  const response = await axios.post<GenerateResumeResponse>('/resume/generate', request);
  return response.data;
};
