# Environment
NODE_ENV=development

# Ports
PORT=3000

# ==============================================
# RAPIDRESUME SPECIFIC CONFIGURATION
# ==============================================

# OpenAI API Key for AI-powered resume generation
# Get your key from: https://platform.openai.com/api-keys
# Required for the /generate endpoint to work
OPENAI_API_KEY=sk-proj-your-openai-api-key-here

# URLs
# These URLs must reference a publicly accessible domain or IP address, not a docker container ID (depending on your compose setup)
PUBLIC_URL=http://localhost:3000
STORAGE_URL=http://localhost:9000/default # default is the bucket name specified in the STORAGE_BUCKET variable

# Database (Prisma/PostgreSQL)
# This can be swapped out to use any other database, like MySQL
# Note: This is used only in the compose.yml file
POSTGRES_PORT=5432
POSTGRES_DB=postgres
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres

# Database (Prisma/PostgreSQL)
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/postgres?schema=public

# Authentication Secrets
# generated with `openssl rand -base64 64`
ACCESS_TOKEN_SECRET=access_token_secret
REFRESH_TOKEN_SECRET=refresh_token_secret

# Chrome Browser (for printing)
# generated with `openssl rand -hex 32`
CHROME_PORT=8080
CHROME_TOKEN=chrome_token
CHROME_URL=ws://localhost:8080
# Launch puppeteer with flag to ignore https errors
# CHROME_IGNORE_HTTPS_ERRORS=true

# Mail Server (for e-mails)
# For testing, you can use https://ethereal.email/create
MAIL_FROM=noreply@localhost
# SMTP_URL=smtp://username:<EMAIL>:587

# Storage
STORAGE_ENDPOINT=localhost
STORAGE_PORT=9000
STORAGE_REGION=us-east-1
STORAGE_BUCKET=default
STORAGE_ACCESS_KEY=minioadmin
STORAGE_SECRET_KEY=minioadmin
STORAGE_USE_SSL=false
STORAGE_SKIP_BUCKET_CHECK=false

# Nx Cloud (Optional)
# NX_CLOUD_ACCESS_TOKEN=

# Crowdin (Optional)
# CROWDIN_PROJECT_ID=
# CROWDIN_PERSONAL_TOKEN=

# Feature Flags (Optional)
# DISABLE_SIGNUPS=false
# DISABLE_EMAIL_AUTH=false

# GitHub (OAuth, Optional)
# GITHUB_CLIENT_ID=
# GITHUB_CLIENT_SECRET=
# GITHUB_CALLBACK_URL=http://localhost:5173/api/auth/github/callback

# Google (OAuth, Optional)
# GOOGLE_CLIENT_ID=
# GOOGLE_CLIENT_SECRET=
# GOOGLE_CALLBACK_URL=http://localhost:5173/api/auth/google/callback

# OpenID (Optional)
# VITE_OPENID_NAME=
# OPENID_AUTHORIZATION_URL=
# OPENID_CALLBACK_URL=http://localhost:5173/api/auth/openid/callback
# OPENID_CLIENT_ID=
# OPENID_CLIENT_SECRET=
# OPENID_ISSUER=
# OPENID_SCOPE=openid profile email
# OPENID_TOKEN_URL=
# OPENID_USER_INFO_URL=
