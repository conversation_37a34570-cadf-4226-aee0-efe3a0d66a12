![RapidResume](https://i.imgur.com/FFc4nyZ.jpg)

![App Version](https://img.shields.io/github/package-json/version/your-username/RapidResume?label=version)
[![Docker Pulls](https://img.shields.io/docker/pulls/rapidresume/rapidresume)](https://hub.docker.com/repository/docker/rapidresume/rapidresume)

# RapidResume

**One-click AI-powered resume generation from job postings.** Built on the foundation of Reactive Resume, RapidResume adds intelligent job-matching capabilities to create tailored resumes instantly.

### [Go to App](https://rapidresume.dev/) | [Docs](https://docs.rapidresume.dev/)

## Description

RapidResume revolutionizes resume creation by combining the power of AI with job-specific optimization. Simply provide a job posting URL or upload a PDF, and RapidResume will automatically generate a tailored resume that maximizes your chances of getting hired.

**Key Innovation:** Our AI analyzes job requirements and intelligently rewrites your resume bullets to match keywords and requirements while preserving your achievements and quantifiable results.

Built on the solid foundation of Reactive Resume, RapidResume maintains all the original features while adding:
- **One-click resume generation** from job postings
- **AI-powered keyword optimization** using GPT-4o
- **Smart bullet point rewriting** that preserves numbers and achievements
- **Job posting analysis** from URLs or PDF uploads
- **Resume history tracking** with quick downloads

Start generating your perfect resume with RapidResume today!

## Templates

| Azurill                                                      | Bronzor                                                     | Chikorita                                                   |
| ------------------------------------------------------------ | ----------------------------------------------------------- | ----------------------------------------------------------- |
| <img src="https://i.imgur.com/jKgo04C.jpeg" width="200px" /> | <img src="https://i.imgur.com/DFNQZP2.jpg" width="200px" /> | <img src="https://i.imgur.com/Dwv8Y7f.jpg" width="200px" /> |

| Ditto                                                       | Kakuna                                                      | Nosepass                                                    |
| ----------------------------------------------------------- | ----------------------------------------------------------- | ----------------------------------------------------------- |
| <img src="https://i.imgur.com/6c5lASL.jpg" width="200px" /> | <img src="https://i.imgur.com/268ML3t.jpg" width="200px" /> | <img src="https://i.imgur.com/npRLsPS.jpg" width="200px" /> |

| Onyx                                                        | Pikachu                                                     | Rhyhorn                                                     |
| ----------------------------------------------------------- | ----------------------------------------------------------- | ----------------------------------------------------------- |
| <img src="https://i.imgur.com/cxplXOW.jpg" width="200px" /> | <img src="https://i.imgur.com/Y9f7qsh.jpg" width="200px" /> | <img src="https://i.imgur.com/h4kQxy2.jpg" width="200px" /> |

## RapidResume Features

### 🚀 AI-Powered Resume Generation
- **One-click workflow**: Paste job URL or upload PDF → Get tailored resume
- **Smart keyword optimization**: AI analyzes job requirements and optimizes your resume
- **Achievement preservation**: Numbers and quantifiable results are kept intact
- **Multiple input methods**: Support for job posting URLs and PDF uploads
- **Resume history**: Track all generated resumes with quick download access

### 📋 Core Features (Inherited from Reactive Resume)
- **Free, forever** and open-source
- No telemetry, user tracking or advertising
- You can self-host the application in less than 30 seconds
- **Available in multiple languages** ([help add/improve your language here](https://translate.rxresu.me/))
- Use your email address (or a throw-away address, no problem) to create an account
- You can also sign in with your GitHub or Google account, and even set up two-factor authentication for extra security
- Create as many resumes as you like under a single account, optimising each resume for every job application based on its description for a higher ATS score
- **Bring your own OpenAI API key** and unlock features such as improving your writing, fixing spelling and grammar or changing the tone of your text in one-click
- Translate your resume into any language using ChatGPT and import it back for easier editing
- Create single page resumes or a resume that spans multiple pages easily
- Customize the colours and layouts to add a personal touch to your resume
- Customise your page layout as you like just by dragging-and-dropping sections
- Create custom sections that are specific to your industry if the existing ones don't fit
- Jot down personal notes specific to your resume that's only visible to you
- Lock a resume to prevent making any further edits (useful for master templates)
- **Dozens of templates** to choose from, ranging from professional to modern
- Design your resume using the standardised EuroPass design template
- Supports printing resumes in A4 or Letter page formats
- Design your resume with any font that's available on [Google Fonts](https://fonts.google.com/)
- **Share a personalised link of your resume** to companies or recruiters for them to get the latest updates
- You can track the number of views or downloads your public resume has received
- Built with state-of-the-art (at the moment) and dependable technologies that's battle tested and peer reviewed by the open-source community on GitHub
- **MIT License**, so do what you like with the code as long as you credit the original author
- And yes, there’s a dark mode too 🌓

## Built With

- React (Vite), for the frontend
- NestJS, for the backend
- Postgres (primary database)
- Prisma ORM, which frees you to switch to any other relational database with a few minor changes in the code
- Minio (for object storage: to store avatars, resume PDFs and previews)
- Browserless (for headless chrome, to print PDFs and generate previews)
- SMTP Server (to send password recovery emails)
- GitHub/Google OAuth (for quickly authenticating users)
- LinguiJS and Crowdin (for translation management and localization)

## Environment Variables

RapidResume requires the following environment variables in addition to the standard Reactive Resume configuration:

### Required for AI Features
```bash
# OpenAI API Key for resume generation
OPENAI_API_KEY=sk-proj-your-openai-api-key-here
```

### Standard Configuration
All other environment variables from Reactive Resume are supported. Key ones include:

```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/rapidresume

# Storage
STORAGE_ENDPOINT=http://localhost:9000
STORAGE_PORT=9000
STORAGE_REGION=us-east-1
STORAGE_BUCKET=rapidresume
STORAGE_ACCESS_KEY=minioadmin
STORAGE_SECRET_KEY=minioadmin
STORAGE_USE_SSL=false

# Authentication
SECRET_KEY=your-secret-key-here
JWT_SECRET=your-jwt-secret-here

# Email (Optional)
MAIL_FROM=<EMAIL>
SMTP_URL=smtp://username:<EMAIL>:587

# Chrome for PDF generation
CHROME_TOKEN=your-browserless-token
CHROME_URL=ws://localhost:3000
```

### Deployment to Fly.io

1. Install the Fly CLI and authenticate
2. Create a new Fly app: `fly apps create rapidresume`
3. Set environment variables:
```bash
fly secrets set OPENAI_API_KEY=sk-proj-your-key-here
fly secrets set DATABASE_URL=your-neon-postgres-url
fly secrets set SECRET_KEY=your-secret-key
# ... other secrets
```
4. Deploy: `fly deploy`

## Star History

<a href="https://star-history.com/#AmruthPillai/Reactive-Resume&Date">
  <picture>
    <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=AmruthPillai/Reactive-Resume&type=Date&theme=dark" />
    <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=AmruthPillai/Reactive-Resume&type=Date" />
    <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=AmruthPillai/Reactive-Resume&type=Date" />
  </picture>
</a>

## License

RapidResume is packaged and distributed using the [MIT License](/LICENSE.md) which allows for commercial use, distribution, modification and private use provided that all copies of the software contain the same license and copyright.

_Built on the foundation of Reactive Resume._
RapidResume - AI-powered resume generation for the modern job seeker.

<p>
  <a href="https://www.digitalocean.com/?utm_medium=opensource&utm_source=Reactive-Resume">
    <img src="https://opensource.nyc3.cdn.digitaloceanspaces.com/attribution/assets/PoweredByDO/DO_Powered_by_Badge_blue.svg" width="200px">
  </a>
</p>
