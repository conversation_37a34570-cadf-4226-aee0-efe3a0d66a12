{"name": "job-parser", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/job-parser/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/job-parser", "main": "libs/job-parser/src/index.ts", "tsConfig": "libs/job-parser/tsconfig.lib.json", "assets": ["libs/job-parser/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/job-parser/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/job-parser/jest.config.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "coverageReporters": ["text"]}}}}, "tags": ["lib:job-parser"]}