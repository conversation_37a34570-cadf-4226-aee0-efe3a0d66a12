import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import * as cheerio from 'cheerio';
import * as pdfParse from 'pdf-parse';
import { firstValueFrom } from 'rxjs';
import { JobData, ParseJobRequest, ParseJobResponse } from './types';

@Injectable()
export class JobParserService {
  private readonly logger = new Logger(JobParserService.name);

  constructor(private readonly httpService: HttpService) {}

  async parseJob(request: ParseJobRequest): Promise<ParseJobResponse> {
    try {
      if (request.jobUrl) {
        return await this.parseJobFromUrl(request.jobUrl);
      } else if (request.fileBuffer) {
        return await this.parseJobFromPdf(request.fileBuffer);
      } else {
        return {
          success: false,
          error: 'Either jobUrl or fileBuffer must be provided',
        };
      }
    } catch (error) {
      this.logger.error('Error parsing job:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  private async parseJobFromUrl(url: string): Promise<ParseJobResponse> {
    try {
      this.logger.log(`Fetching job posting from URL: ${url}`);
      
      const response = await firstValueFrom(
        this.httpService.get(url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
          },
          timeout: 10000,
        })
      );

      const $ = cheerio.load(response.data);
      
      // Remove script and style elements
      $('script, style, nav, header, footer, aside').remove();
      
      // Extract text content
      const rawText = $('body').text().replace(/\s+/g, ' ').trim();
      
      // Parse the job data from the cleaned text
      const jobData = this.extractJobDataFromText(rawText, $);
      
      return {
        success: true,
        data: jobData,
      };
    } catch (error) {
      this.logger.error(`Error fetching job from URL ${url}:`, error);
      return {
        success: false,
        error: `Failed to fetch job posting: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private async parseJobFromPdf(buffer: Buffer): Promise<ParseJobResponse> {
    try {
      this.logger.log('Parsing job posting from PDF');
      
      const pdfData = await pdfParse(buffer);
      const rawText = pdfData.text;
      
      // Parse the job data from the extracted text
      const jobData = this.extractJobDataFromText(rawText);
      
      return {
        success: true,
        data: jobData,
      };
    } catch (error) {
      this.logger.error('Error parsing PDF:', error);
      return {
        success: false,
        error: `Failed to parse PDF: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  private extractJobDataFromText(text: string, $?: cheerio.CheerioAPI): JobData {
    // Clean up the text
    const cleanText = text.replace(/\s+/g, ' ').trim();
    
    // Extract job title - look for common patterns
    let title = 'Software Engineer'; // Default fallback
    const titlePatterns = [
      /(?:job title|position|role):\s*([^\n\r.]+)/i,
      /(?:we are looking for|seeking|hiring)\s+(?:a|an)?\s*([^\n\r.]+?)(?:\s+to|\s+who|\s+with)/i,
      /<title[^>]*>([^<]+)</i,
    ];
    
    for (const pattern of titlePatterns) {
      const match = cleanText.match(pattern);
      if (match && match[1]) {
        title = match[1].trim();
        break;
      }
    }

    // If using cheerio, try to extract from common selectors
    if ($) {
      const titleSelectors = [
        'h1', '.job-title', '.position-title', '[data-testid*="title"]',
        '.title', '#job-title', '.job-header h1', '.posting-headline h2'
      ];
      
      for (const selector of titleSelectors) {
        const element = $(selector).first();
        if (element.length && element.text().trim()) {
          title = element.text().trim();
          break;
        }
      }
    }

    // Extract company name
    let company = 'Company'; // Default fallback
    const companyPatterns = [
      /(?:company|employer|organization):\s*([^\n\r.]+)/i,
      /(?:at|@)\s+([A-Z][a-zA-Z\s&.,-]+?)(?:\s+we|\s+is|\s+in|\s+\()/,
      /([A-Z][a-zA-Z\s&.,-]+?)\s+(?:is looking for|seeks|hiring)/i,
    ];
    
    for (const pattern of companyPatterns) {
      const match = cleanText.match(pattern);
      if (match && match[1]) {
        company = match[1].trim();
        break;
      }
    }

    // If using cheerio, try to extract company from common selectors
    if ($) {
      const companySelectors = [
        '.company-name', '.employer', '[data-testid*="company"]',
        '.company', '#company-name', '.job-header .company'
      ];
      
      for (const selector of companySelectors) {
        const element = $(selector).first();
        if (element.length && element.text().trim()) {
          company = element.text().trim();
          break;
        }
      }
    }

    // Extract responsibilities
    const responsibilities = this.extractListItems(cleanText, [
      /(?:responsibilities|duties|you will|what you'll do)[\s\S]*?(?=(?:requirements|qualifications|skills|benefits|about|$))/i,
    ]);

    // Extract requirements
    const requirements = this.extractListItems(cleanText, [
      /(?:requirements|qualifications|skills|must have|you have|ideal candidate)[\s\S]*?(?=(?:responsibilities|benefits|about|nice to have|$))/i,
    ]);

    // Extract location
    let location: string | undefined;
    const locationPatterns = [
      /(?:location|based in|office in):\s*([^\n\r.]+)/i,
      /([A-Z][a-zA-Z\s]+,\s*[A-Z]{2}(?:\s+\d{5})?)/,
      /(Remote|Hybrid|On-site)/i,
    ];
    
    for (const pattern of locationPatterns) {
      const match = cleanText.match(pattern);
      if (match && match[1]) {
        location = match[1].trim();
        break;
      }
    }

    return {
      title,
      company,
      description: cleanText.substring(0, 1000), // First 1000 chars as description
      responsibilities,
      requirements,
      location,
      rawText: cleanText,
    };
  }

  private extractListItems(text: string, sectionPatterns: RegExp[]): string[] {
    const items: string[] = [];
    
    for (const pattern of sectionPatterns) {
      const match = text.match(pattern);
      if (match && match[0]) {
        const sectionText = match[0];
        
        // Look for bullet points, numbered lists, or line breaks
        const bulletPatterns = [
          /[•·▪▫◦‣⁃]\s*([^\n\r•·▪▫◦‣⁃]+)/g,
          /^\s*[-*]\s*([^\n\r-*]+)/gm,
          /^\s*\d+\.?\s*([^\n\r\d]+)/gm,
          /(?:^|\n)\s*([A-Z][^\n\r.!?]*[.!?])/gm,
        ];
        
        for (const bulletPattern of bulletPatterns) {
          let match;
          while ((match = bulletPattern.exec(sectionText)) !== null) {
            const item = match[1].trim();
            if (item.length > 10 && item.length < 200) { // Reasonable length
              items.push(item);
            }
          }
          if (items.length > 0) break; // Use first successful pattern
        }
        
        if (items.length > 0) break; // Use first successful section
      }
    }
    
    // If no structured list found, try to extract sentences
    if (items.length === 0) {
      const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20);
      items.push(...sentences.slice(0, 5).map(s => s.trim()));
    }
    
    return items.slice(0, 10); // Limit to 10 items
  }
}
