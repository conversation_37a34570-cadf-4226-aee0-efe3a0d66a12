import { HttpService } from '@nestjs/axios';
import { Test, TestingModule } from '@nestjs/testing';
import { of } from 'rxjs';
import { JobParserService } from './job-parser.service';

describe('JobParserService', () => {
  let service: JobParserService;
  let httpService: HttpService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobParserService,
        {
          provide: HttpService,
          useValue: {
            get: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<JobParserService>(JobParserService);
    httpService = module.get<HttpService>(HttpService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('parseJob', () => {
    it('should return error when neither jobUrl nor fileBuffer is provided', async () => {
      const result = await service.parseJob({});
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Either jobUrl or fileBuffer must be provided');
    });

    it('should parse job from URL successfully', async () => {
      const mockHtml = `
        <html>
          <head><title>Software Engineer - TechCorp</title></head>
          <body>
            <h1>Software Engineer</h1>
            <div class="company">TechCorp</div>
            <div class="description">
              We are looking for a Software Engineer to join our team.
              Responsibilities:
              • Develop web applications
              • Write clean code
              • Collaborate with team members
              
              Requirements:
              • 3+ years of experience
              • JavaScript proficiency
              • React knowledge
            </div>
          </body>
        </html>
      `;

      jest.spyOn(httpService, 'get').mockReturnValue(
        of({ data: mockHtml } as any)
      );

      const result = await service.parseJob({
        jobUrl: 'https://example.com/job',
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.title).toContain('Software Engineer');
      expect(result.data?.company).toBeDefined();
      expect(result.data?.responsibilities).toBeInstanceOf(Array);
      expect(result.data?.requirements).toBeInstanceOf(Array);
    });

    it('should handle URL fetch errors gracefully', async () => {
      jest.spyOn(httpService, 'get').mockImplementation(() => {
        throw new Error('Network error');
      });

      const result = await service.parseJob({
        jobUrl: 'https://invalid-url.com/job',
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Failed to fetch job posting');
    });

    it('should parse job from PDF buffer', async () => {
      // Mock PDF buffer - in a real test, you'd use a proper PDF buffer
      const mockPdfBuffer = Buffer.from('Mock PDF content with job details');
      
      // Mock pdf-parse to return structured data
      const mockPdfParse = jest.fn().mockResolvedValue({
        text: `
          Software Engineer Position at TechCorp
          
          Job Description:
          We are seeking a talented Software Engineer to join our development team.
          
          Responsibilities:
          - Develop and maintain web applications
          - Write clean, efficient code
          - Participate in code reviews
          
          Requirements:
          - Bachelor's degree in Computer Science
          - 3+ years of JavaScript experience
          - Experience with React and Node.js
        `,
      });

      // Replace the actual pdf-parse with our mock
      (service as any).parseJobFromPdf = jest.fn().mockImplementation(async (buffer) => {
        const pdfData = await mockPdfParse(buffer);
        const jobData = (service as any).extractJobDataFromText(pdfData.text);
        return {
          success: true,
          data: jobData,
        };
      });

      const result = await service.parseJob({
        fileBuffer: mockPdfBuffer,
      });

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.rawText).toContain('Software Engineer');
    });
  });

  describe('extractJobDataFromText', () => {
    it('should extract job title from text', () => {
      const text = 'Job Title: Senior Software Engineer at TechCorp';
      const result = (service as any).extractJobDataFromText(text);
      
      expect(result.title).toContain('Senior Software Engineer');
    });

    it('should extract company name from text', () => {
      const text = 'TechCorp is looking for a Software Engineer';
      const result = (service as any).extractJobDataFromText(text);
      
      expect(result.company).toContain('TechCorp');
    });

    it('should extract responsibilities from bulleted list', () => {
      const text = `
        Responsibilities:
        • Develop web applications
        • Write clean code
        • Collaborate with team
      `;
      const result = (service as any).extractJobDataFromText(text);
      
      expect(result.responsibilities).toBeInstanceOf(Array);
      expect(result.responsibilities.length).toBeGreaterThan(0);
    });

    it('should extract requirements from numbered list', () => {
      const text = `
        Requirements:
        1. 3+ years of experience
        2. JavaScript proficiency
        3. React knowledge
      `;
      const result = (service as any).extractJobDataFromText(text);
      
      expect(result.requirements).toBeInstanceOf(Array);
      expect(result.requirements.length).toBeGreaterThan(0);
    });
  });
});
