import { createZodDto } from "nestjs-zod/dto";
import { z } from "zod";

export const generateResumeSchema = z.object({
  jobUrl: z.string().url().optional(),
  fileId: z.string().optional(),
  baselineResumeId: z.string().optional(), // If not provided, use user's latest resume
}).refine(
  (data) => data.jobUrl || data.fileId,
  {
    message: "Either jobUrl or fileId must be provided",
    path: ["jobUrl"],
  }
);

export class GenerateResumeDto extends createZodDto(generateResumeSchema) {}

export const generateResumeResponseSchema = z.object({
  success: z.boolean(),
  resumeId: z.string().optional(),
  jobTitle: z.string().optional(),
  company: z.string().optional(),
  error: z.string().optional(),
});

export class GenerateResumeResponseDto extends createZodDto(generateResumeResponseSchema) {}
